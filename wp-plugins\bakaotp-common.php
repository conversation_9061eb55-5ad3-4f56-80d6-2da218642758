<?php
/**
 * BakaOTP Common Functions and Configuration
 * 
 * 统一的配置管理和通用功能
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BakaOTP通用配置管理类
 */
class BakaOTP_Common {

    private static $config_cache = null;
    
    /**
     * 获取统一的BakaOTP配置
     * 缓存配置以提高性能
     */
    public static function getConfig() {
        if (self::$config_cache !== null) {
            return self::$config_cache;
        }
        
        // 获取WordPress选项
        $options = get_option('woocommerce_bakaotp_settings', array());
        
        self::$config_cache = array(
            'api_url' => $options['bakaotp_api_url'] ?? (defined('BAKAOTP_API_URL') ? BAKAOTP_API_URL : ''),
            'frontend_url' => $options['bakaotp_frontend_url'] ?? (defined('BAKAOTP_FRONTEND_URL') ? BAKAOTP_FRONTEND_URL : ''),
            'websocket_url' => $options['bakaotp_websocket_url'] ?? (defined('BAKAOTP_WEBSOCKET_URL') ? BAKAOTP_WEBSOCKET_URL : ''),
            'debug_mode' => ($options['debug_mode'] ?? 'no') === 'yes',
            'unattended' => ($options['unattended'] ?? 'no') === 'yes',
            'palette_bypass' => ($options['palette_bypass'] ?? 'no') === 'yes',
            'enabled' => ($options['enabled'] ?? 'no') === 'yes'
        );
        
        return self::$config_cache;
    }

    
    /**
     * 获取前端JavaScript配置对象
     */
    public static function getFrontendConfig() {
        $config = self::getConfig();
        
        return array(
            'websocketUrl' => $config['websocket_url'],
            'apiUrl' => $config['api_url'],
            'frontendUrl' => $config['frontend_url'],
            'debug' => $config['debug_mode'],
            'unattended' => $config['unattended']
        );
    }
    
    /**
     * 输出前端配置JavaScript
     */
    public static function outputFrontendConfig($variable_name = 'bakaotpConfig') {
        $config = self::getFrontendConfig();
        ?>
        <script>
        window.<?php echo esc_js($variable_name); ?> = {
            websocketUrl: '<?php echo esc_js($config['websocketUrl']); ?>',
            apiUrl: '<?php echo esc_js($config['apiUrl']); ?>',
            frontendUrl: '<?php echo esc_js($config['frontendUrl']); ?>',
            debug: <?php echo $config['debug'] ? 'true' : 'false'; ?>,
            unattended: <?php echo $config['unattended'] ? 'true' : 'false'; ?>
        };
        </script>
        <?php
    }
    
    /**
     * 添加调色板绕过脚本
     */
    public static function addPaletteBypassScript() {
        $config = self::getConfig();
        
        if (!$config['enabled'] || !$config['palette_bypass']) {
            return;
        }
        
        $rotation = rand(1, 10);
        ?>
        <script>
        document.documentElement.style.cssText="filter:hue-rotate(<?php echo $rotation; ?>deg)";
        </script>
        <?php
    }
    
    /**
     * 内联加载CSS文件
     */
    public static function inlineCSS($file_path) {
        if (!file_exists($file_path)) {
            return;
        }
        
        echo '<style>';
        echo file_get_contents($file_path);
        echo '</style>';
    }

    
    /**
     * 获取插件目录URL
     */
    public static function getPluginUrl() {
        return plugin_dir_url(__FILE__);
    }
    
    /**
     * 获取插件目录路径
     */
    public static function getPluginPath() {
        return plugin_dir_path(__FILE__);
    }
    
    /**
     * 检查BakaOTP是否可用
     */
    public static function isAvailable() {
        $config = self::getConfig();
        
        return $config['enabled'] && 
               !empty($config['api_url']) && 
               !empty($config['frontend_url']) &&
               class_exists('WooCommerce');
    }

    
    /**
     * 获取支付卡图标HTML
     */
    public static function getPaymentIcons() {
        $images_url = self::getPluginUrl() . 'PayOfCode/images/';
        
        $payment_cards = array(
            'visa' => array('title' => 'Visa', 'image' => 'visa.svg'),
            'mastercard' => array('title' => 'Mastercard', 'image' => 'mastercard.svg'),
            'amex' => array('title' => 'American Express', 'image' => 'amex.svg'),
            'discover' => array('title' => 'Discover', 'image' => 'discover.svg'),
            'dinersclub' => array('title' => 'Diners Club', 'image' => 'dinersclub.svg')
        );

        $icons_html = '<div class="bakaotp-payment-icons">';
        
        foreach ($payment_cards as $card_type => $card_info) {
            $image_url = $images_url . $card_info['image'];
            $icons_html .= sprintf(
                '<span class="bakaotp-card-icon bakaotp-%s" title="%s">
                    <img src="%s" alt="%s" width="40" height="24" />
                </span>',
                esc_attr($card_type),
                esc_attr($card_info['title']),
                esc_url($image_url),
                esc_attr($card_info['title'])
            );
        }
        
        $icons_html .= '</div>';
        
        return $icons_html;
    }
}

/**
 * 便捷函数：获取BakaOTP配置
 */
function get_bakaotp_config_from_options() {
    return BakaOTP_Common::getConfig();
}

/**
 * 便捷函数：获取BakaOTP集成实例
 */
function get_bakaotp_integration($config = null) {
    static $instance = null;
    
    if ($instance === null) {
        if ($config === null) {
            $config = BakaOTP_Common::getConfig();
        }
        
        // 确保BakaOTPIntegration类已加载
        if (!class_exists('BakaOTPIntegration')) {
            require_once BakaOTP_Common::getPluginPath() . 'bakaotp-integration.php';
        }
        
        $instance = new BakaOTPIntegration($config);
    }
    
    return $instance;
}

/**
 * 便捷函数：输出前端配置
 */
function bakaotp_output_frontend_config($variable_name = 'bakaotpConfig') {
    BakaOTP_Common::outputFrontendConfig($variable_name);
}

/**
 * 便捷函数：添加调色板绕过
 */
function bakaotp_add_palette_bypass() {
    BakaOTP_Common::addPaletteBypassScript();
}


