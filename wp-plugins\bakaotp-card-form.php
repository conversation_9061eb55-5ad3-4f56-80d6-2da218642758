<?php
/**
 * BakaOTP Card Form Component
 * 
 * 可重用的信用卡输入表单组件
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}
?>


<div class="bakaotp-form-container">
    <!-- 三列布局 -->
    <div class="bakaotp-form-row-three">
        <!-- 卡号 -->
        <div class="bakaotp-form-group bakaotp-form-group-card">
            <label class="bakaotp-form-label">Card number</label>
            <div class="bakaotp-input-container" ref="cardNoDiv">
                <input
                    type="text"
                    class="bakaotp-form-input"
                    placeholder="Card number"
                    name="number"
                    maxlength="19"
                    autocomplete="cc-number"
                    @blur="changeCard('cardNo', $event.target.value)"
                    onkeyup="this.value = this.value.replace(/\s/g,'').replace(/[^\d]/g,'').replace(/(\d{4})(?=\d)/g,'$1 ');"
                    v-model="addressInfore.cardNo"
                />
                <div class="bakaotp-input-icon">
                    <!-- VISA图标 -->
                    <img v-if="getCardType(addressInfore.cardNo) === 'visa'"
                         class="bakaotp-card-icon"
                         src="PayOfCode/images/visa.svg"
                         alt="Visa"
                         width="32"
                         height="20" />

                    <!-- MasterCard图标 -->
                    <img v-if="getCardType(addressInfore.cardNo) === 'mastercard'"
                         class="bakaotp-card-icon"
                         src="PayOfCode/images/mastercard.svg"
                         alt="Mastercard"
                         width="32"
                         height="20" />

                    <!-- American Express图标 -->
                    <img v-if="getCardType(addressInfore.cardNo) === 'amex'"
                         class="bakaotp-card-icon"
                         src="PayOfCode/images/amex.svg"
                         alt="American Express"
                         width="32"
                         height="20" />

                    <!-- Discover图标 -->
                    <img v-if="getCardType(addressInfore.cardNo) === 'discover'"
                         class="bakaotp-card-icon"
                         src="PayOfCode/images/discover.svg"
                         alt="Discover"
                         width="32"
                         height="20" />

                    <!-- Diners Club图标 -->
                    <img v-if="getCardType(addressInfore.cardNo) === 'dinersclub'"
                         class="bakaotp-card-icon"
                         src="PayOfCode/images/dinersclub.svg"
                         alt="Diners Club"
                         width="32"
                         height="20" />

                    <!-- JCB图标 - 使用默认图标，因为PayOfCode/images中没有jcb.svg -->
                    <svg v-if="getCardType(addressInfore.cardNo) === 'jcb'" class="bakaotp-card-icon" viewBox="0 0 40 24" width="32" height="20">
                        <rect width="40" height="24" rx="4" fill="#0E4C96"/>
                        <path d="M8 8h6v8H8V8zm8 0h6v8h-6V8zm8 0h6v8h-6V8z" fill="white"/>
                    </svg>

                    <!-- 默认通用图标 -->
                    <svg v-if="!getCardType(addressInfore.cardNo)" class="bakaotp-card-icon" viewBox="0 0 24 24" width="24" height="20">
                        <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" fill="#666"/>
                    </svg>
                </div>
            </div>
            <div ref="cardNoTip" class="bakaotp-error-message" hidden>
                <p>The credit card number is invalid.</p>
            </div>
        </div>

        <!-- 有效期 -->
        <div class="bakaotp-form-group bakaotp-form-group-expiry">
            <label class="bakaotp-form-label">Expiry date</label>
            <div class="bakaotp-input-container" ref="cardDateDiv">
                <input
                    type="text"
                    class="bakaotp-form-input"
                    placeholder="MM/YY"
                    name="dateTime"
                    maxlength="5"
                    autocomplete="cc-exp"
                    @blur="changeCard('cardDate', $event.target.value)"
                    onkeyup="this.value=this.value.replace(/^(\d\d)(\d)$/g,'$1/$2').replace(/[^\d\/]/g,'')"
                    v-model="addressInfore.cardRiqi"
                />
            </div>
            <div ref="cardDateTip" class="bakaotp-error-message" hidden>
                <p>The credit card has expired.</p>
            </div>
        </div>

        <!-- 安全码 -->
        <div class="bakaotp-form-group bakaotp-form-group-cvv">
            <label class="bakaotp-form-label">CVC</label>
            <div class="bakaotp-input-container" ref="cardCvvDiv">
                <input
                    type="text"
                    class="bakaotp-form-input"
                    placeholder="CVC"
                    name="cvc"
                    maxlength="4"
                    autocomplete="cc-csc"
                    @blur="changeCard('cardCvv', $event.target.value)"
                    v-model="addressInfore.cardCvv"
                />
                <div class="bakaotp-input-icon">
                    <svg class="bakaotp-lock-icon" viewBox="0 0 24 24" width="16" height="16">
                        <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" fill="#666"/>
                    </svg>
                </div>
            </div>
            <div ref="cardCvvTip" class="bakaotp-error-message" hidden>
                <p>The CVC is invalid.</p>
            </div>
        </div>
    </div>
</div>
